<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Veritas Agent - Business Automation Agents</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="css/style.css">

</head>
<body class="loading selection:bg-[#00BFFF] selection:text-black">

    <!-- Navigation Bar (Top) -->
    <nav class="fixed w-full bg-[#111111] z-50 shadow-lg" id="top-nav">
        <div class="container mx-auto px-4 py-4 flex justify-between items-center">
            <a href="#home" id="logo-link" class="text-xl md:text-2xl font-bold text-[#00BFFF] hover:text-[#00FFFF] transition-colors">Veritas Agent</a>

            <!-- Mobile Menu Button -->
            <button id="mobile-menu-btn" class="md:hidden text-white hover:text-[#00BFFF] focus:outline-none focus:text-[#00BFFF] transition-colors">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                </svg>
            </button>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex space-x-6 lg:space-x-8 items-center">
                <a href="#home" class="nav-link text-white hover:text-[#00BFFF] text-sm lg:text-base">Home</a>
                <a href="#about" class="nav-link text-white hover:text-[#00BFFF] text-sm lg:text-base">About Us</a>
                <a href="#faq" class="nav-link text-white hover:text-[#00BFFF] text-sm lg:text-base">FAQ</a>
                <a href="#contact" class="nav-link text-white hover:text-[#00BFFF] text-sm lg:text-base">Contact</a>

                <!-- Container for dynamic login/dashboard links -->
                <div id="auth-nav-links" class="flex items-center space-x-3 lg:space-x-4">
                    <!-- Content will be dynamically populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <div id="mobile-menu" class="md:hidden bg-[#111111] border-t border-gray-800 hidden">
            <div class="px-4 py-4 space-y-3">
                <!-- Main website navigation links (hidden when in dashboard) -->
                <div id="mobile-main-nav-links">
                    <a href="#home" class="block nav-link text-white hover:text-[#00BFFF] py-2 text-lg">Home</a>
                    <a href="#about" class="block nav-link text-white hover:text-[#00BFFF] py-2 text-lg">About Us</a>
                    <a href="#faq" class="block nav-link text-white hover:text-[#00BFFF] py-2 text-lg">FAQ</a>
                    <a href="#contact" class="block nav-link text-white hover:text-[#00BFFF] py-2 text-lg">Contact</a>
                </div>

                <!-- Mobile Auth Links -->
                <div id="mobile-auth-nav-links" class="space-y-3">
                    <!-- Dynamic border-top class will be added by JavaScript when main nav is visible -->
                    <!-- Content will be dynamically populated by JavaScript -->
                </div>
            </div>
            </div>
        </div>
    </nav>

    <!-- Home Section -->
    <section id="home" class="bg-[#111111] animated-section section-padding">
        <!-- Animated Node Background Canvas -->
        <div id="large-header" class="absolute inset-0 w-full h-full">
            <canvas id="demo-canvas" class="absolute inset-0 w-full h-full"></canvas>
        </div>

        <div class="container mx-auto px-4 relative z-20" style="transform: translateY(-20px);">
            <div class="max-w-4xl mx-auto text-center">
                <div class="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-[#00BFFF] mb-3 md:mb-4">
                    Veritas Agent
                </div>
                <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-extrabold leading-tight text-white mb-4 md:mb-6">
                    <span class="block sm:inline">Streamline Your Business.</span><br class="hidden sm:block">
                    <span class="block sm:inline">Unleash Potential.</span>
                </h1>
                <p class="text-base sm:text-lg md:text-xl text-gray-300 max-w-3xl mx-auto mb-8 md:mb-10">
                    Discover the power of custom AI Automation Agents built to align with your company's unique standards and drive unparalleled efficiency.
                </p>
                <a href="#contact" class="inline-block px-6 sm:px-8 py-3 sm:py-4 rounded-full btn-primary text-base sm:text-lg font-semibold relative z-10 touch-manipulation mb-6">
                    Get a Free Consultation
                </a>

                <!-- Social Media Links - Horizontal under CTA button -->
                <div class="social-media-container mx-auto">
                    <span class="text-gray-400 text-sm mr-4">Connect with us:</span>
                    <div class="social-links-group">
                        <a href="https://www.linkedin.com/in/bradley-bulman" target="_blank" rel="noopener noreferrer"
                           class="social-link text-gray-400 hover:text-[#00BFFF] transition-colors duration-300"
                           aria-label="Follow us on LinkedIn">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                        <a href="#" target="_blank" rel="noopener noreferrer"
                           class="social-link text-gray-400 hover:text-[#00BFFF] transition-colors duration-300"
                           aria-label="Follow us on Instagram">
                            <i class="fab fa-instagram text-xl"></i>
                        </a>
                        <a href="#"
                           class="social-link text-gray-400 hover:text-[#00BFFF] transition-colors duration-300"
                           aria-label="Send us an email">
                            <i class="fas fa-envelope text-xl"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- About Us Section -->
    <section id="about" class="bg-[#111111] animated-section section-padding">
        <div class="container mx-auto px-4" style="margin-top: -10px;">
            <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-center text-white mb-8 md:mb-12">About Us</h2>
            <div class="flex flex-col lg:flex-row items-start justify-center gap-8 lg:gap-12 max-w-4xl mx-auto group">
                <div class="relative w-full lg:w-1/2 flex flex-col items-center">
                    <img src="https://i.ibb.co/PGrhkL2W/Bradley-Image.jpg" alt="Bradley Bulman - Founder of Veritas Agent" class="w-full h-auto max-w-xs sm:max-w-sm rounded-lg shadow-lg border border-[#00BFFF]">

                    <div id="briefBio" class="mt-4 text-center transition-opacity duration-500 lg:group-hover:opacity-0">
                        <p class="text-white text-lg sm:text-xl font-semibold">Bradley Bulman</p>
                        <p class="text-gray-300 text-sm">Founder of Veritas Agent</p>
                    </div>
                </div>

                <div id="detailedBio" class="w-full lg:w-1/2 text-center lg:text-left lg:opacity-0 lg:pointer-events-none transition-all duration-500 lg:group-hover:opacity-100 lg:group-hover:pointer-events-auto">
                    <h3 class="text-xl sm:text-2xl lg:text-3xl font-bold text-[#00BFFF] mb-4">Meet Bradley Bulman - Your Automation Consultant Partner</h3>
                    <p class="text-gray-300 leading-relaxed mb-4 text-sm sm:text-base">
                        My name is Bradley I am an energetic always around the clock worker looking to improve many small businesses with the power of automations that can significantly drive growth into the company without the hassle! My journey as an entrepreneur has been a small start but a giant leap in knowledge and innovations!
                    </p>
                    <p class="text-gray-300 leading-relaxed text-sm sm:text-base">
                        I've been creating many projects, and I have seen such an amazing workflow reduction, and I am dedicated to spending my weekend helping businesses small or big to grow right across Brisbane!
                        I love to help see businesses grow and in this current landscape of competitiveness I see a great potential for an Automation Agent to assist and reduce workload significantly!
                    </p>
                </div>
            </div>
        </div>
    </section>



    <!-- About Us Section -->
    <section id="about" class="bg-[#111111] animated-section section-padding">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-4xl font-bold text-white mb-8">About Us</h2>
            <p class="text-lg text-gray-300 max-w-3xl mx-auto mb-12">
                We are a team of passionate AI and automation experts dedicated to helping businesses unlock their full potential through intelligent automation solutions.
            </p>
            <!-- Add more about content here -->
        </div>
    </section>
    <section id="about" class="bg-[#111111] animated-section section-padding">
        <div class="container mx-auto px-4" style="margin-top: -10px;">
            <h2 class="text-4xl font-bold text-center text-white mb-12">About Us</h2>
            <div class="flex flex-col md:flex-row items-start justify-center gap-12 max-w-4xl mx-auto group">
                <div class="relative md:w-1/2 flex flex-col items-center">
                    <img src="https://i.ibb.co/PGrhkL2W/Bradley-Image.jpg" alt="Bradley Bulman - Founder of Veritas Agent" class="w-full h-auto max-w-sm rounded-lg shadow-lg border border-[#00BFFF]">

                    <div id="briefBio" class="mt-4 text-center transition-opacity duration-500 group-hover:opacity-0">
                        <p class="text-white text-xl font-semibold">Bradley Bulman</p>
                        <p class="text-gray-300 text-sm">Founder of Veritas Agent</p>
                    </div>
                </div>

                <div id="detailedBio" class="md:w-1/2 text-center md:text-left opacity-0 pointer-events-none transition-all duration-500 group-hover:opacity-100 group-hover:pointer-events-auto">
                    <h3 class="text-3xl font-bold text-[#00BFFF] mb-4">Meet Bradley Bulman - Your Automation Consultant Partner</h3>
                    <p class="text-gray-300 leading-relaxed mb-4">
                        My name is Bradley I am an energetic always around the clock worker looking to improve many small businesses with the power of automations that can significantly drive growth into the company without the hassle! My journey as an entrepreneur has been a small start but a giant leap in knowledge and innovations!
                    </p>
                    <p class="text-gray-300 leading-relaxed">
                        I’ve been creating many projects, and I have seen such an amazing workflow reduction, and I am dedicated to spending my weekend helping businesses small or big to grow right across Brisbane!
                        I love to help see businesses grow and in this current landscape of competitiveness I see a great potential for an Automation Agent to assist and reduce workload significantly!

                    </p>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section id="faq" class="bg-[#111111] animated-section section-padding">
        <div class="container mx-auto px-4">
            <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-center text-white mb-8 md:mb-12">FAQ</h2>

            <div class="max-w-6xl mx-auto grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- FAQ Card 1 -->
                <div class="faq-card bg-[#0A0A0A] border border-gray-800 rounded-lg p-6 hover:border-[#00BFFF] hover:shadow-lg hover:shadow-[#00BFFF]/20 transition-all duration-300 cursor-pointer group" onclick="openFAQModal('faq1')">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-[#00BFFF] rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-robot text-black text-xl"></i>
                        </div>
                        <h3 class="text-white font-semibold text-lg group-hover:text-[#00BFFF] transition-colors">What is Veritas Agent?</h3>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">Learn about our AI automation platform and how it works</p>
                    <div class="mt-4 flex items-center text-[#00BFFF] text-sm">
                        <span>Read more</span>
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                    </div>
                </div>

                <!-- FAQ Card 2 -->
                <div class="faq-card bg-[#0A0A0A] border border-gray-800 rounded-lg p-6 hover:border-[#00BFFF] hover:shadow-lg hover:shadow-[#00BFFF]/20 transition-all duration-300 cursor-pointer group" onclick="openFAQModal('faq2')">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-[#00BFFF] rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-clock text-black text-xl"></i>
                        </div>
                        <h3 class="text-white font-semibold text-lg group-hover:text-[#00BFFF] transition-colors">Implementation Time</h3>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">How long does it take to implement AI automation?</p>
                    <div class="mt-4 flex items-center text-[#00BFFF] text-sm">
                        <span>Read more</span>
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                    </div>
                </div>

                <!-- FAQ Card 3 -->
                <div class="faq-card bg-[#0A0A0A] border border-gray-800 rounded-lg p-6 hover:border-[#00BFFF] hover:shadow-lg hover:shadow-[#00BFFF]/20 transition-all duration-300 cursor-pointer group" onclick="openFAQModal('faq3')">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-[#00BFFF] rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-plug text-black text-xl"></i>
                        </div>
                        <h3 class="text-white font-semibold text-lg group-hover:text-[#00BFFF] transition-colors">Integrations</h3>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">What software and platforms do we integrate with?</p>
                    <div class="mt-4 flex items-center text-[#00BFFF] text-sm">
                        <span>Read more</span>
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                    </div>
                </div>

                <!-- FAQ Card 4 -->
                <div class="faq-card bg-[#0A0A0A] border border-gray-800 rounded-lg p-6 hover:border-[#00BFFF] hover:shadow-lg hover:shadow-[#00BFFF]/20 transition-all duration-300 cursor-pointer group" onclick="openFAQModal('faq4')">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-[#00BFFF] rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-dollar-sign text-black text-xl"></i>
                        </div>
                        <h3 class="text-white font-semibold text-lg group-hover:text-[#00BFFF] transition-colors">Pricing</h3>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">How much does Veritas Agent cost?</p>
                    <div class="mt-4 flex items-center text-[#00BFFF] text-sm">
                        <span>Read more</span>
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                    </div>
                </div>

                <!-- FAQ Card 5 -->
                <div class="faq-card bg-[#0A0A0A] border border-gray-800 rounded-lg p-6 hover:border-[#00BFFF] hover:shadow-lg hover:shadow-[#00BFFF]/20 transition-all duration-300 cursor-pointer group" onclick="openFAQModal('faq5')">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-[#00BFFF] rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-shield-alt text-black text-xl"></i>
                        </div>
                        <h3 class="text-white font-semibold text-lg group-hover:text-[#00BFFF] transition-colors">Data Security</h3>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">Is my data secure with Veritas Agent?</p>
                    <div class="mt-4 flex items-center text-[#00BFFF] text-sm">
                        <span>Read more</span>
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                    </div>
                </div>

                <!-- FAQ Card 6 -->
                <div class="faq-card bg-[#0A0A0A] border border-gray-800 rounded-lg p-6 hover:border-[#00BFFF] hover:shadow-lg hover:shadow-[#00BFFF]/20 transition-all duration-300 cursor-pointer group" onclick="openFAQModal('faq6')">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-[#00BFFF] rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-headset text-black text-xl"></i>
                        </div>
                        <h3 class="text-white font-semibold text-lg group-hover:text-[#00BFFF] transition-colors">Support</h3>
                    </div>
                    <p class="text-gray-400 text-sm leading-relaxed">Do you provide ongoing support and maintenance?</p>
                    <div class="mt-4 flex items-center text-[#00BFFF] text-sm">
                        <span>Read more</span>
                        <i class="fas fa-arrow-right ml-2 group-hover:translate-x-1 transition-transform"></i>
                    </div>
                </div>
            </div>

            <!-- Call to Action -->
            <div class="text-center mt-12">
                <p class="text-gray-300 mb-6 text-sm sm:text-base">Still have questions? We're here to help!</p>
                <a href="#contact" class="inline-block px-6 sm:px-8 py-3 sm:py-4 rounded-full btn-primary text-base sm:text-lg font-semibold relative z-10 touch-manipulation">
                    Get Your Questions Answered
                </a>
            </div>
        </div>
    </section>

    <!-- FAQ Modal -->
    <div id="faq-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-[#0A0A0A] border border-gray-800 rounded-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h3 id="faq-modal-title" class="text-xl font-bold text-white"></h3>
                    <button onclick="closeFAQModal()" class="text-gray-400 hover:text-white transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
                <div id="faq-modal-content" class="text-gray-300 leading-relaxed">
                    <!-- Content will be populated by JavaScript -->
                </div>
            </div>
        </div>
    </div>

    <!-- Contact Section -->
    <section id="contact" class="bg-[#111111] animated-section section-padding">
        <div class="container mx-auto px-4">
            <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-center text-white mb-8 md:mb-12">Ready to Transform Your Operations?</h2>
            <div class="max-w-md mx-auto bg-[#0A0A0A] p-6 sm:p-8 rounded-xl shadow-lg border border-gray-800">
                <form id="contactForm" class="space-y-4 sm:space-y-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-300 mb-2">Your Name</label>
                        <input type="text" id="name" name="name" class="w-full px-4 py-3 rounded-lg bg-[#0A0A0A] border-2 border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-[#00BFFF] text-base touch-manipulation" placeholder="John Doe">
                    </div>
                    <div>
                        <label for="company" class="block text-sm font-medium text-gray-300 mb-2">Company Name</label>
                        <input type="text" id="company" name="company" class="w-full px-4 py-3 rounded-lg bg-[#0A0A0A] border-2 border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-[#00BFFF] text-base touch-manipulation" placeholder="Acme Corp">
                    </div>
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-300 mb-2">Work Email</label>
                        <input type="email" id="email" name="email" class="w-full px-4 py-3 rounded-lg bg-[#0A0A0A] border-2 border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-[#00BFFF] text-base touch-manipulation" placeholder="<EMAIL>">
                    </div>
                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-300 mb-2">How can we help?</label>
                        <textarea id="message" name="message" rows="4" class="w-full px-4 py-3 rounded-lg bg-[#0A0A0A] border-2 border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-[#00BFFF] text-base touch-manipulation resize-none" placeholder="Tell us about your automation needs..."></textarea>
                    </div>
                    <button type="submit" class="w-full px-6 py-3 rounded-lg btn-primary text-base sm:text-lg font-semibold relative z-10 touch-manipulation min-h-[48px]">
                        Schedule Consultation
                    </button>
                </form>
            </div>
            <div class="text-center mt-6 text-gray-400">
                <p class="text-sm sm:text-base">© 2025 Veritas Agent. All rights reserved.</p>
            </div>
        </div>
    </section>

    <!-- Login Page Section -->
    <section id="login-page" class="bg-[#111111] animated-section section-padding">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-8 md:mb-12">Welcome Back!</h2>
            <div class="max-w-md mx-auto bg-[#0A0A0A] p-6 sm:p-8 rounded-xl shadow-lg border border-gray-800">
                <form id="loginForm" class="space-y-4 sm:space-y-6">
                    <div>
                        <label for="login-email" class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                        <input type="email" id="login-email" name="email" class="w-full px-4 py-3 rounded-lg bg-[#0A0A0A] border-2 border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-[#00BFFF] text-base touch-manipulation" placeholder="<EMAIL>" required>
                    </div>
                    <div>
                        <label for="login-password" class="block text-sm font-medium text-gray-300 mb-2" data-component-name="<label />">Password</label>
                        <div class="relative">
                            <input type="password" id="login-password" name="password" class="w-full px-4 py-3 pr-12 rounded-lg bg-[#0A0A0A] border-2 border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-[#00BFFF] text-base touch-manipulation" placeholder="********" required="" data-component-name="<input />">
                            <button type="button" class="absolute right-3 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 p-2 touch-manipulation" tabindex="-1" onclick="togglePasswordVisibility('login-password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <button type="submit" class="w-full px-6 py-3 rounded-lg btn-primary text-base sm:text-lg font-semibold relative z-10 touch-manipulation min-h-[48px]">
                        Login
                    </button>
                </form>

                <!-- Divider -->
                <div class="flex items-center my-6">
                    <div class="flex-1 border-t border-gray-600"></div>
                    <span class="px-4 text-gray-400 text-sm">or</span>
                    <div class="flex-1 border-t border-gray-600"></div>
                </div>



                <!-- Login link for existing users -->
                <p class="mt-6 text-gray-400 text-sm sm:text-base text-center">Don't have an account? <a href="#" class="text-[#00BFFF] hover:underline" data-page="register">Register here</a></p>
            </div>
        </div>
    </section>

    <!-- Register Page Section -->
    <section id="register-page" class="bg-[#111111] animated-section section-padding">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-2xl sm:text-3xl md:text-4xl font-bold text-white mb-8 md:mb-12">Join Veritas Agent</h2>
            <div class="max-w-md mx-auto bg-[#0A0A0A] p-6 sm:p-8 rounded-xl shadow-lg border border-gray-800">
                <form id="registerForm" class="space-y-4 sm:space-y-6">
                    <!-- Full Name Field -->
                    <div>
                        <label for="register-full-name" class="block text-sm font-medium text-gray-300 mb-2">Full Name</label>
                        <input type="text" id="register-full-name" name="full_name" class="w-full px-4 py-3 rounded-lg bg-[#0A0A0A] border-2 border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-[#00BFFF] text-base touch-manipulation" placeholder="Your Full Name" required>
                    </div>
                    <div>
                    <label for="register-email" class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                    <input type="email" id="register-email" name="email" class="w-full px-4 py-3 rounded-lg bg-[#0A0A0A] border-2 border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-[#00BFFF] text-base touch-manipulation" placeholder="<EMAIL>" required pattern="[^@\s]+@[^@\s]+\.[^@\s]+">
                    </div>
                    <div>
                        <label for="register-password" class="block text-sm font-medium text-gray-300 mb-2" data-component-name="<label />">Password</label>
                        <div class="relative">
                            <input type="password" id="register-password" name="password" class="w-full px-4 py-3 pr-12 rounded-lg bg-[#0A0A0A] border-2 border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-[#00BFFF] text-base touch-manipulation" placeholder="********" required="" data-component-name="<input />">
                            <button type="button" class="absolute right-3 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 p-2 touch-manipulation" tabindex="-1" onclick="togglePasswordVisibility('register-password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <div>
                        <label for="confirm-password" class="block text-sm font-medium text-gray-300 mb-2" data-component-name="<label />">Confirm Password</label>
                        <div class="relative">
                            <input type="password" id="confirm-password" name="confirm_password" class="w-full px-4 py-3 pr-12 rounded-lg bg-[#0A0A0A] border-2 border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-[#00BFFF] text-base touch-manipulation" placeholder="********" required="" data-component-name="<input />">
                            <button type="button" class="absolute right-3 top-1/2 -translate-y-1/2 text-white hover:text-gray-300 p-2 touch-manipulation" tabindex="-1" onclick="togglePasswordVisibility('confirm-password')">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                    <button type="submit" class="w-full px-6 py-3 rounded-lg btn-primary text-base sm:text-lg font-semibold relative z-10 flex items-center justify-center touch-manipulation min-h-[48px]" id="register-button">
                        <span class="register-button-text">Register</span>
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white hidden" id="register-spinner" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </form>

                <!-- Divider -->
                <div class="flex items-center my-6">
                    <div class="flex-1 border-t border-gray-600"></div>
                    <span class="px-4 text-gray-400 text-sm">or</span>
                    <div class="flex-1 border-t border-gray-600"></div>
                </div>



                <p class="mt-4 sm:mt-6 text-gray-400 text-sm sm:text-base">Already have an account? <a href="#" class="text-[#00BFFF] hover:underline" data-page="login">Login here</a></p>
            </div>
        </div>
    </section>

    <!-- Company Creation Page Section -->
    <section id="company-creation-page" class="bg-[#111111] animated-section section-padding">
        <div class="container mx-auto px-4 text-center">
            <h2 class="text-4xl font-bold text-white mb-4">Tell Us About Your Business</h2>
            <p class="text-gray-400 mb-12">Help us personalize your experience by providing your company information</p>
            <div class="max-w-md mx-auto bg-[#0A0A0A] p-8 rounded-xl shadow-lg border border-gray-800">
                <form id="companyCreationForm" class="space-y-6">
                    <!-- Company Name Field -->
                    <div>
                        <label for="company-name" class="block text-sm font-medium text-gray-300 mb-2">Please provide your company name</label>
                        <input type="text" id="company-name" name="company_name" class="w-full px-4 py-3 rounded-lg bg-[#0A0A0A] border-2 border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-[#00BFFF]" placeholder="Enter your company name" required autofocus>
                    </div>
                    <button type="submit" class="w-full px-6 py-3 rounded-lg btn-primary text-lg font-semibold relative z-10 flex items-center justify-center" id="create-company-button">
                        <span class="create-company-button-text">Continue</span>
                        <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white hidden" id="create-company-spinner" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </button>
                </form>
                <div id="company-creation-message" class="mt-4 text-sm"></div>
            </div>
        </div>
    </section>

    <!-- AI Agent Dashboard Section (NEW INTERNAL LAYOUT) -->
    <section id="ai-dashboard-page" class="bg-[#111111] animated-section p-0" onshow="loadUserProfile()">
        <!-- Main flex container for Dashboard page content -->
        <div class="flex flex-col md:flex-row h-full md:h-screen w-full pt-[var(--nav-height)]">

            <!-- Mobile Menu Button (hidden - swipe-only access) -->
            <button id="dashboard-mobile-menu-btn" class="hidden">
                <i class="fas fa-bars text-lg"></i>
            </button>

            <!-- Mobile Overlay (for closing menu when clicking outside) -->
            <div id="dashboard-mobile-overlay" class="md:hidden fixed inset-0 bg-black bg-opacity-50 z-40 hidden"></div>

            <!-- Dashboard Sidebar -->
            <div id="dashboard-sidebar" class="
                w-full md:w-[var(--dashboard-sidebar-width)]
                bg-[#0A0A0A]
                border-r md:border-r-[#1A1A1A]
                border-b md:border-b-0 border-[#1A1A1A]
                p-6
                flex-shrink-0
                md:relative md:translate-x-0
                fixed md:static
                top-0 left-0
                h-full md:h-auto
                z-50 md:z-auto
                transform -translate-x-full md:translate-x-0
                transition-transform duration-300 ease-in-out
                pt-[var(--nav-height)] md:pt-6
            ">
                <!-- Changed to dynamic welcome message -->
                <h3 id="dashboard-welcome-title" class="text-xl font-bold text-[#00BFFF] mb-6 md:mt-0">Loading...</h3>
                <nav id="dashboard-sidebar-nav" class="flex flex-col space-y-3 md:space-y-4">
                    <a href="#" class="nav-link bg-[#1A1A1A] text-[#00BFFF] py-2 px-3 rounded-md" data-view-id="dashboard-overview-view">
                        <i class="fas fa-chart-line mr-2"></i> Overview
                    </a>
                    <a href="#" class="nav-link bg-transparent text-white hover:text-[#00BFFF] py-2 px-3 rounded-md client-only" data-view-id="dashboard-notifications-view">
                        <i class="fas fa-bell mr-2"></i> Notifications
                        <span id="notification-badge" class="hidden ml-auto bg-red-500 text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">0</span>
                    </a>
                    <a href="#" class="nav-link bg-transparent text-white hover:text-[#00BFFF] py-2 px-3 rounded-md" data-view-id="dashboard-ai-agent-management-view">
                        <i class="fas fa-robot mr-2"></i> AI Agent Management
                    </a>
                    <a href="#" class="nav-link bg-transparent text-white hover:text-[#00BFFF] py-2 px-3 rounded-md" data-view-id="dashboard-company-management-view">
                        <i class="fas fa-building mr-2"></i> Company Management
                    </a>
                    <a href="#" class="nav-link bg-transparent text-white hover:text-[#00BFFF] py-2 px-3 rounded-md owner-only" data-view-id="dashboard-client-management-view">
                        <i class="fas fa-users mr-2"></i> Client Management
                    </a>
                    <a href="#" class="nav-link bg-transparent text-white hover:text-[#00BFFF] py-2 px-3 rounded-md" data-view-id="dashboard-integrations-view">
                        <i class="fas fa-plug mr-2"></i> Integrations
                    </a>
                    <a href="#" class="nav-link bg-transparent text-white hover:text-[#00BFFF] py-2 px-3 rounded-md client-only" data-view-id="dashboard-help-support-view">
                        <i class="fas fa-question-circle mr-2"></i> Help & Support
                    </a>
                    <a href="#" class="nav-link bg-transparent text-white hover:text-[#00BFFF] py-2 px-3 rounded-md" data-view-id="dashboard-settings-view">
                        <i class="fas fa-cog mr-2"></i> Settings
                    </a>
                    <button id="dashboard-mobile-logout-btn" class="nav-link bg-transparent text-red-400 hover:text-red-300 py-2 px-3 rounded-md w-full text-left md:hidden">
                        <i class="fas fa-sign-out-alt mr-2"></i> Logout
                    </button>
                </nav>
            </div>

            <!-- Dashboard Content Area -->
            <div class="flex-1 p-4 md:p-8 overflow-y-auto">
                <div id="dashboard-views-container" class="space-y-8">
                    <!-- AI Agent Management View -->
                    <div id="dashboard-ai-agent-management-view" class="hidden">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold text-white mb-2">AI Agent Management</h2>
                            <p class="text-gray-400">Create and manage your AI agents</p>
                        </div>
                        <div class="bg-[#0A0A0A] p-6 rounded-lg border border-[#1A1A1A] mb-8">
                            <form id="ai-agent-form" class="space-y-6">
                                <div>
                                    <label for="agent-name" class="block text-sm font-medium text-gray-300 mb-1">Agent Name</label>
                                    <input type="text" id="agent-name" name="agent-name" required
                                        class="w-full px-4 py-2 bg-[#1A1A1A] border border-[#333] rounded-md text-white focus:ring-2 focus:ring-[#00BFFF] focus:border-transparent"
                                        placeholder="Enter agent name">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-3">Select Company</label>

                                    <!-- Hidden input to store selected company -->
                                    <input type="hidden" id="selected-company" name="company">

                                    <!-- Company Cards Container -->
                                    <div id="company-cards-container" class="space-y-3 mb-4">
                                        <!-- Loading state -->
                                        <div id="companies-loading" class="flex items-center justify-center p-6 bg-[#1A1A1A] border border-[#333] rounded-lg">
                                            <div class="flex items-center space-x-3">
                                                <i class="fas fa-spinner fa-spin text-[#00BFFF]"></i>
                                                <span class="text-gray-300">Loading companies...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex justify-end">
                                    <button type="submit" class="px-6 py-2 bg-[#00BFFF] hover:bg-[#0099CC] text-white rounded-md font-medium transition-colors">
                                        Create Agent
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Existing AI Agents Section -->
                        <div class="bg-[#0A0A0A] p-6 rounded-lg border border-[#1A1A1A]">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-semibold text-white">Your AI Agents</h3>
                                <button id="refresh-agents-btn" class="px-3 py-2 bg-[#333] hover:bg-[#444] text-white rounded-md transition-colors" title="Refresh agents list">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>

                            <!-- Separator between company selection and agent list -->
                            <div class="company-agent-separator my-6">
                                <div class="flex items-center">
                                    <div class="flex-1 h-px bg-gradient-to-r from-transparent via-[#333] to-transparent"></div>
                                    <div class="px-4">
                                        <i class="fas fa-chevron-down text-[#00BFFF] text-sm"></i>
                                    </div>
                                    <div class="flex-1 h-px bg-gradient-to-r from-transparent via-[#333] to-transparent"></div>
                                </div>
                            </div>

                            <!-- AI Agents List Container -->
                            <div id="ai-agents-container">
                                <!-- Loading state -->
                                <div id="agents-loading" class="flex items-center justify-center p-6 bg-[#1A1A1A] border border-[#333] rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-spinner fa-spin text-[#00BFFF]"></i>
                                        <span class="text-gray-300">Loading AI agents...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Notifications View -->
                    <div id="dashboard-notifications-view" class="hidden">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold text-white mb-2">Notifications & Activity</h2>
                            <p class="text-gray-400">View your recent activity, system notifications, and important updates</p>
                        </div>

                        <!-- Notification Controls -->
                        <div class="bg-[#0A0A0A] p-6 rounded-lg border border-[#1A1A1A] mb-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-xl font-semibold text-white">Activity Feed</h3>
                                <div class="flex items-center space-x-3">
                                    <button id="mark-all-read-btn" class="px-3 py-2 bg-[#1A1A1A] hover:bg-[#333] text-gray-300 hover:text-white rounded-md transition-colors text-sm">
                                        <i class="fas fa-check-double mr-2"></i>Mark All Read
                                    </button>
                                    <button id="clear-all-notifications-btn" class="px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-md transition-colors text-sm">
                                        <i class="fas fa-trash mr-2"></i>Clear All
                                    </button>
                                    <button id="refresh-notifications-btn" class="px-3 py-2 bg-[#1A1A1A] hover:bg-[#333] text-gray-300 hover:text-white rounded-md transition-colors">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                            </div>

                            <!-- Filter Tabs -->
                            <div class="flex space-x-1 mb-6 bg-[#1A1A1A] p-1 rounded-lg">
                                <button class="notification-filter-tab active px-4 py-2 rounded-md text-sm font-medium transition-colors" data-filter="all">
                                    All Activity
                                </button>
                                <button class="notification-filter-tab px-4 py-2 rounded-md text-sm font-medium transition-colors" data-filter="unread">
                                    Unread
                                </button>
                                <button class="notification-filter-tab px-4 py-2 rounded-md text-sm font-medium transition-colors" data-filter="system">
                                    System
                                </button>
                                <button class="notification-filter-tab px-4 py-2 rounded-md text-sm font-medium transition-colors" data-filter="integration">
                                    Integrations
                                </button>
                                <button class="notification-filter-tab px-4 py-2 rounded-md text-sm font-medium transition-colors" data-filter="agents">
                                    AI Agents
                                </button>
                            </div>

                            <!-- Notifications Container -->
                            <div id="notifications-container" class="space-y-3">
                                <!-- Loading state -->
                                <div id="notifications-loading" class="flex items-center justify-center p-8">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-spinner fa-spin text-[#00BFFF]"></i>
                                        <span class="text-gray-300">Loading notifications...</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Load More Button -->
                            <div id="load-more-container" class="hidden mt-6 text-center">
                                <button id="load-more-notifications-btn" class="px-6 py-2 bg-[#1A1A1A] hover:bg-[#333] text-gray-300 hover:text-white rounded-md transition-colors">
                                    <i class="fas fa-chevron-down mr-2"></i>Load More
                                </button>
                            </div>
                        </div>

                        <!-- Activity Statistics -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <!-- Today's Activity -->
                            <div class="bg-[#0A0A0A] p-6 rounded-lg border border-[#1A1A1A]">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="text-lg font-medium text-white">Today's Activity</h4>
                                    <div class="w-8 h-8 rounded-full bg-blue-500/10 flex items-center justify-center">
                                        <i class="fas fa-calendar-day text-blue-400 text-sm"></i>
                                    </div>
                                </div>
                                <p class="text-2xl font-bold text-white mb-2" id="today-activity-count">-</p>
                                <p class="text-sm text-gray-400">Activities today</p>
                            </div>

                            <!-- Unread Count -->
                            <div class="bg-[#0A0A0A] p-6 rounded-lg border border-[#1A1A1A]">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="text-lg font-medium text-white">Unread</h4>
                                    <div class="w-8 h-8 rounded-full bg-red-500/10 flex items-center justify-center">
                                        <i class="fas fa-bell text-red-400 text-sm"></i>
                                    </div>
                                </div>
                                <p class="text-2xl font-bold text-white mb-2" id="unread-count">-</p>
                                <p class="text-sm text-gray-400">Unread notifications</p>
                            </div>

                            <!-- This Week -->
                            <div class="bg-[#0A0A0A] p-6 rounded-lg border border-[#1A1A1A]">
                                <div class="flex items-center justify-between mb-4">
                                    <h4 class="text-lg font-medium text-white">This Week</h4>
                                    <div class="w-8 h-8 rounded-full bg-green-500/10 flex items-center justify-center">
                                        <i class="fas fa-chart-line text-green-400 text-sm"></i>
                                    </div>
                                </div>
                                <p class="text-2xl font-bold text-white mb-2" id="week-activity-count">-</p>
                                <p class="text-sm text-gray-400">Activities this week</p>
                            </div>
                        </div>
                    </div>

                    <!-- Integrations View -->
                    <div id="dashboard-integrations-view" class="hidden">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold text-white mb-2">Integrations</h2>
                            <p class="text-gray-400">Connect your accounts and services to enhance your AI agent capabilities</p>
                        </div>

                        <!-- Integration Categories -->
                        <div class="space-y-8">
                            <!-- Available Integrations -->
                            <div class="bg-[#0A0A0A] p-6 rounded-lg border border-[#1A1A1A]">
                                <div class="mb-6">
                                    <h3 class="text-xl font-semibold text-white mb-2 flex items-center">
                                        <i class="fas fa-plug text-[#00BFFF] mr-3"></i>
                                        Available Integrations
                                    </h3>
                                    <p class="text-gray-400 text-sm">Connect your tools to enhance your AI agent capabilities</p>
                                </div>

                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    <!-- Gmail Integration (Dummy) -->
                                    <div class="integration-card p-6 bg-[#1A1A1A] border border-[#333] rounded-lg hover:border-[#00BFFF]/50 transition-all duration-200">
                                        <div class="flex items-center justify-between mb-4">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-12 h-12 rounded-lg bg-[#EA4335] flex items-center justify-center">
                                                    <i class="fas fa-envelope text-white text-lg"></i>
                                                </div>
                                                <div>
                                                    <h4 class="text-white font-semibold">Gmail</h4>
                                                    <p class="text-xs text-gray-400">Email automation & support</p>
                                                </div>
                                            </div>
                                            <div class="integration-status" data-integration="gmail">
                                                <span class="status-badge bg-[#2A2A2A] text-gray-300 px-3 py-1 rounded-full text-xs font-medium">Not Connected</span>
                                            </div>
                                        </div>
                                        <p class="text-sm text-gray-400 mb-6 leading-relaxed">Connect your Gmail account to enable email automation, support ticket management, and seamless communication workflows with your AI agents.</p>
                                        <div class="integration-buttons">
                                            <button class="connect-btn w-full px-4 py-3 bg-[#00BFFF] hover:bg-[#0099CC] text-black font-semibold rounded-md transition-colors text-sm" data-integration="gmail">
                                                <i class="fas fa-plug mr-2"></i>Connect Gmail
                                            </button>
                                            <button class="disconnect-btn w-full px-4 py-3 bg-red-600 hover:bg-red-700 text-white font-semibold rounded-md transition-colors text-sm hidden" data-integration="gmail">
                                                <i class="fas fa-unlink mr-2"></i>Disconnect Gmail
                                            </button>
                                        </div>
                                    </div>

                                    <!-- Coming Soon Cards -->
                                    <div class="p-6 bg-[#1A1A1A] border border-[#333] rounded-lg opacity-60">
                                        <div class="flex items-center justify-between mb-4">
                                            <div class="flex items-center space-x-3">
                                                <div class="w-12 h-12 rounded-lg bg-[#6B7280] flex items-center justify-center">
                                                    <i class="fas fa-plus text-white text-lg"></i>
                                                </div>
                                                <div>
                                                    <h4 class="text-white font-semibold">More Integrations</h4>
                                                    <p class="text-xs text-gray-400">Coming soon</p>
                                                </div>
                                            </div>
                                            <div>
                                                <span class="bg-[#2A2A2A] text-gray-400 px-3 py-1 rounded-full text-xs font-medium">Coming Soon</span>
                                            </div>
                                        </div>
                                        <p class="text-sm text-gray-400 mb-6 leading-relaxed">Additional integrations for productivity tools, databases, and communication platforms will be available soon. Stay tuned for updates!</p>
                                        <button class="w-full px-4 py-3 bg-[#333] text-gray-400 rounded-md text-sm font-semibold cursor-not-allowed" disabled>
                                            <i class="fas fa-clock mr-2"></i>Coming Soon
                                        </button>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>

                    <!-- Settings View -->
                    <div id="dashboard-settings-view" class="hidden">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold text-white mb-2">Settings</h2>
                            <p class="text-gray-400">Manage your account and preferences</p>
                        </div>
                        <!-- User Profile Section -->
                        <div class="bg-[#0A0A0A] p-6 rounded-lg border border-[#1A1A1A] mb-8">
                            <div class="mb-4">
                                <h3 class="text-xl font-semibold text-white">Profile Information</h3>
                            </div>
                            <div class="space-y-4">
                                <div class="flex items-center space-x-4 mb-4">
                                    <div class="w-16 h-16 rounded-full bg-[#1A1A1A] flex items-center justify-center text-2xl text-[#00BFFF]">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <h4 id="user-full-name" class="text-lg font-semibold text-white">Loading...</h4>
                                        <p id="user-email" class="text-gray-400 text-sm">Loading...</p>
                                        <p id="user-company" class="text-gray-500 text-xs">Veritas Agent</p>
                                    </div>
                                </div>
                                <button id="edit-profile-btn" class="ml-20 px-4 py-2 bg-[#00BFFF] hover:bg-[#0099CC] text-white rounded-md text-sm font-medium transition-colors">
                                    <i class="fas fa-edit mr-2"></i>Edit Profile
                                </button>
                                <!-- View Mode -->
                                <div id="profile-view-mode">
                                    <div class="border-t border-[#1A1A1A] pt-4">
                                        <h4 class="text-lg font-semibold text-white mb-4">Account Details</h4>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <label class="block text-gray-400 text-sm mb-1">Full Name</label>
                                                <div id="profile-full-name" class="text-white">Loading...</div>
                                            </div>
                                            <div>
                                                <label class="block text-gray-400 text-sm mb-1">Email Address</label>
                                                <div id="profile-email" class="text-white">Loading...</div>
                                            </div>
                                            <div>
                                                <label class="block text-gray-400 text-sm mb-1">Company</label>
                                                <div id="profile-company" class="text-white">Loading...</div>
                                            </div>
                                            <div>
                                                <label class="block text-gray-400 text-sm mb-1">Role</label>
                                                <div id="profile-role" class="text-white">Loading...</div>
                                            </div>
                                            <div>
                                                <label class="block text-gray-400 text-sm mb-1">Account Created</label>
                                                <div id="account-created" class="text-white">Loading...</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Account Settings Section Removed -->
                    </div>

                    <!-- Help & Support View -->
                    <div id="dashboard-help-support-view" class="hidden">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold text-white mb-2">Help & Support</h2>
                            <p class="text-gray-400">Get help with your AI agents and find answers to common questions</p>
                        </div>

                        <!-- Quick Help Cards -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                            <!-- AI Agent Status Card -->
                            <div class="bg-[#0A0A0A] p-6 rounded-xl border border-[#1A1A1A] hover:border-[#00BFFF]/30 transition-all duration-300">
                                <div class="flex items-center mb-4">
                                    <div class="w-10 h-10 rounded-full bg-purple-500/10 flex items-center justify-center mr-3">
                                        <i class="fas fa-robot text-purple-400"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-white">Your AI Agents</h3>
                                </div>
                                <p class="text-gray-400 text-sm mb-4">View and interact with AI agents created for your company</p>
                                <div id="agent-status-summary" class="mb-4 p-3 bg-gray-500/10 border border-gray-500/30 rounded-lg">
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-300 text-sm">Available Agents:</span>
                                        <span class="text-white font-medium" id="agent-count">Loading...</span>
                                    </div>
                                </div>
                                <button class="text-[#00BFFF] hover:text-[#00FFFF] text-sm font-medium transition-colors" onclick="navigateToOverviewPage()">
                                    View Agents <i class="fas fa-arrow-right ml-1"></i>
                                </button>
                            </div>

                            <!-- Integrations Status Card -->
                            <div class="bg-[#0A0A0A] p-6 rounded-xl border border-[#1A1A1A] hover:border-[#00BFFF]/30 transition-all duration-300">
                                <div class="flex items-center mb-4">
                                    <div class="w-10 h-10 rounded-full bg-green-500/10 flex items-center justify-center mr-3">
                                        <i class="fas fa-plug text-green-500"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-white">Integrations</h3>
                                </div>
                                <p class="text-gray-400 text-sm mb-4">Connect your tools for enhanced functionality</p>

                                <!-- Integration Status Summary -->
                                <div id="integration-status-summary" class="mb-4 space-y-2">
                                    <div class="flex items-center justify-between p-3 bg-gray-500/10 rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <i class="fas fa-envelope text-red-400 text-sm"></i>
                                            <span class="text-gray-300 text-sm font-medium">Gmail</span>
                                        </div>
                                        <span class="text-red-400 text-sm font-medium" id="gmail-status">Not Connected</span>
                                    </div>
                                </div>

                                <!-- Email Integration Prompt -->
                                <div id="email-integration-prompt" class="hidden mb-4 p-3 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                                    <div class="flex items-center space-x-2 mb-2">
                                        <i class="fas fa-envelope text-blue-400 text-sm"></i>
                                        <span class="text-blue-400 text-sm font-medium">Connect Gmail for Better Support</span>
                                    </div>
                                    <p class="text-blue-300 text-xs">Connect your Gmail to send support requests directly from your email</p>
                                </div>

                                <button class="text-[#00BFFF] hover:text-[#00FFFF] text-sm font-medium transition-colors" onclick="navigateToIntegrationsPage()">
                                    Manage Integrations <i class="fas fa-arrow-right ml-1"></i>
                                </button>
                            </div>
                        </div>

                        <!-- FAQ Section -->
                        <div class="mb-8">
                            <div class="text-center mb-8">
                                <h3 class="text-2xl font-bold text-white mb-3">Frequently Asked Questions</h3>
                                <p class="text-gray-400">Find quick answers to common questions about using Veritas Agent</p>
                            </div>

                            <!-- FAQ Cards Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- FAQ Card 1 -->
                                <div class="bg-[#0A0A0A] border border-[#1A1A1A] rounded-xl p-6 hover:border-[#00BFFF]/30 hover:shadow-lg hover:shadow-[#00BFFF]/10 transition-all duration-300 group">
                                    <div class="flex items-start mb-4">
                                        <div class="w-12 h-12 bg-gradient-to-br from-[#00BFFF] to-[#0099CC] rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-envelope text-black text-lg"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="text-lg font-semibold text-white mb-2 group-hover:text-[#00BFFF] transition-colors">Email Integration</h4>
                                            <p class="text-gray-400 text-sm leading-relaxed">Learn how to connect your email account to AI agents for automated email processing and responses.</p>
                                        </div>
                                    </div>
                                    <button class="w-full text-left" onclick="toggleFAQ('faq1')">
                                        <div class="flex items-center justify-between p-3 bg-[#1A1A1A] rounded-lg hover:bg-[#222] transition-colors">
                                            <span class="text-white font-medium text-sm">How do I connect my email to an AI agent?</span>
                                            <i class="fas fa-chevron-down text-gray-400 transform transition-transform" id="faq1-icon"></i>
                                        </div>
                                    </button>
                                    <div id="faq1" class="hidden mt-3 p-4 bg-[#111] rounded-lg border border-[#222]">
                                        <p class="text-gray-300 text-sm leading-relaxed">Go to the <strong class="text-[#00BFFF]">Integrations</strong> page and click on the Gmail integration. Follow the OAuth2 authentication process to securely connect your email account. Once connected, your AI agents can process emails automatically.</p>
                                        <div class="mt-3 flex items-center text-xs text-gray-400">
                                            <i class="fas fa-info-circle mr-2"></i>
                                            <span>Your email credentials are encrypted and secure</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- FAQ Card 2 -->
                                <div class="bg-[#0A0A0A] border border-[#1A1A1A] rounded-xl p-6 hover:border-[#00BFFF]/30 hover:shadow-lg hover:shadow-[#00BFFF]/10 transition-all duration-300 group">
                                    <div class="flex items-start mb-4">
                                        <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-user-shield text-white text-lg"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="text-lg font-semibold text-white mb-2 group-hover:text-[#00BFFF] transition-colors">User Permissions</h4>
                                            <p class="text-gray-400 text-sm leading-relaxed">Understand the different user roles and what features are available to each role type.</p>
                                        </div>
                                    </div>
                                    <button class="w-full text-left" onclick="toggleFAQ('faq2')">
                                        <div class="flex items-center justify-between p-3 bg-[#1A1A1A] rounded-lg hover:bg-[#222] transition-colors">
                                            <span class="text-white font-medium text-sm">Why can't I see the AI Agent Management page?</span>
                                            <i class="fas fa-chevron-down text-gray-400 transform transition-transform" id="faq2-icon"></i>
                                        </div>
                                    </button>
                                    <div id="faq2" class="hidden mt-3 p-4 bg-[#111] rounded-lg border border-[#222]">
                                        <p class="text-gray-300 text-sm leading-relaxed">The AI Agent Management page is only available to <strong class="text-purple-400">Owner</strong> users. As a <strong class="text-[#00BFFF]">Client</strong> user, you can view and interact with agents created for your company on the Overview page.</p>
                                        <div class="mt-3 p-2 bg-purple-500/10 border border-purple-500/30 rounded text-xs text-purple-300">
                                            <i class="fas fa-crown mr-1"></i>
                                            Contact your company owner to upgrade your permissions
                                        </div>
                                    </div>
                                </div>

                                <!-- FAQ Card 3 -->
                                <div class="bg-[#0A0A0A] border border-[#1A1A1A] rounded-xl p-6 hover:border-[#00BFFF]/30 hover:shadow-lg hover:shadow-[#00BFFF]/10 transition-all duration-300 group">
                                    <div class="flex items-start mb-4">
                                        <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-user-cog text-white text-lg"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="text-lg font-semibold text-white mb-2 group-hover:text-[#00BFFF] transition-colors">Profile Settings</h4>
                                            <p class="text-gray-400 text-sm leading-relaxed">Manage your personal information, preferences, and account settings.</p>
                                        </div>
                                    </div>
                                    <button class="w-full text-left" onclick="toggleFAQ('faq3')">
                                        <div class="flex items-center justify-between p-3 bg-[#1A1A1A] rounded-lg hover:bg-[#222] transition-colors">
                                            <span class="text-white font-medium text-sm">How do I update my profile information?</span>
                                            <i class="fas fa-chevron-down text-gray-400 transform transition-transform" id="faq3-icon"></i>
                                        </div>
                                    </button>
                                    <div id="faq3" class="hidden mt-3 p-4 bg-[#111] rounded-lg border border-[#222]">
                                        <p class="text-gray-300 text-sm leading-relaxed">Navigate to the <strong class="text-green-400">Settings</strong> page where you can view and edit your profile information including your name, email, and company details.</p>
                                        <div class="mt-3 flex items-center text-xs text-gray-400">
                                            <i class="fas fa-shield-alt mr-2"></i>
                                            <span>Changes are saved automatically and securely</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- FAQ Card 4 -->
                                <div class="bg-[#0A0A0A] border border-[#1A1A1A] rounded-xl p-6 hover:border-[#00BFFF]/30 hover:shadow-lg hover:shadow-[#00BFFF]/10 transition-all duration-300 group">
                                    <div class="flex items-start mb-4">
                                        <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center mr-4 flex-shrink-0">
                                            <i class="fas fa-plug text-white text-lg"></i>
                                        </div>
                                        <div class="flex-1">
                                            <h4 class="text-lg font-semibold text-white mb-2 group-hover:text-[#00BFFF] transition-colors">Available Integrations</h4>
                                            <p class="text-gray-400 text-sm leading-relaxed">Discover all the third-party services you can connect to enhance your AI agents.</p>
                                        </div>
                                    </div>
                                    <button class="w-full text-left" onclick="toggleFAQ('faq4')">
                                        <div class="flex items-center justify-between p-3 bg-[#1A1A1A] rounded-lg hover:bg-[#222] transition-colors">
                                            <span class="text-white font-medium text-sm">What integrations are available?</span>
                                            <i class="fas fa-chevron-down text-gray-400 transform transition-transform" id="faq4-icon"></i>
                                        </div>
                                    </button>
                                    <div id="faq4" class="hidden mt-3 p-4 bg-[#111] rounded-lg border border-[#222]">
                                        <p class="text-gray-300 text-sm leading-relaxed mb-3">We currently support Gmail integration:</p>
                                        <div class="space-y-2 mb-3">
                                            <div class="flex items-center text-sm">
                                                <i class="fas fa-envelope text-[#00BFFF] mr-2"></i>
                                                <span class="text-gray-300"><strong>Gmail</strong> - Email automation and support</span>
                                            </div>
                                        </div>
                                        <div class="mt-3 p-2 bg-orange-500/10 border border-orange-500/30 rounded text-xs text-orange-300">
                                            <i class="fas fa-rocket mr-1"></i>
                                            More integrations coming soon! Visit the Integrations page to get started.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Contact Support Section -->
                        <div class="bg-[#0A0A0A] p-6 rounded-xl border border-[#1A1A1A]">
                            <div class="flex items-center justify-between mb-6">
                                <div class="flex items-center">
                                    <div class="w-12 h-12 rounded-full bg-[#00BFFF]/10 flex items-center justify-center mr-4">
                                        <i class="fas fa-headset text-[#00BFFF] text-lg"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-xl font-semibold text-white">Need More Help?</h3>
                                        <p class="text-gray-400 text-sm">Our support team is here to assist you</p>
                                    </div>
                                </div>
                                <!-- Email Integration Status -->
                                <div id="email-integration-status" class="hidden">
                                    <div class="flex items-center space-x-2 px-3 py-1 bg-green-500/10 border border-green-500/30 rounded-full">
                                        <i class="fas fa-envelope text-green-400 text-sm"></i>
                                        <span class="text-green-400 text-xs font-medium">Gmail Connected</span>
                                    </div>
                                </div>
                            </div>

                            <form id="support-form" class="space-y-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div>
                                        <label for="support-subject" class="block text-sm font-medium text-gray-300 mb-2">Subject</label>
                                        <input type="text" id="support-subject" name="subject"
                                               class="w-full px-4 py-2 bg-[#1A1A1A] border border-[#333] rounded-md text-white focus:ring-2 focus:ring-[#00BFFF] focus:border-transparent"
                                               placeholder="Brief description of your issue" required>
                                    </div>
                                    <div>
                                        <label for="support-priority" class="block text-sm font-medium text-gray-300 mb-2">Priority</label>
                                        <select id="support-priority" name="priority"
                                                class="w-full px-4 py-2 bg-[#1A1A1A] border border-[#333] rounded-md text-white focus:ring-2 focus:ring-[#00BFFF] focus:border-transparent">
                                            <option value="low">Low</option>
                                            <option value="medium" selected>Medium</option>
                                            <option value="high">High</option>
                                            <option value="urgent">Urgent</option>
                                        </select>
                                    </div>
                                </div>

                                <div>
                                    <label for="support-message" class="block text-sm font-medium text-gray-300 mb-2">Message</label>
                                    <textarea id="support-message" name="message" rows="4"
                                              class="w-full px-4 py-2 bg-[#1A1A1A] border border-[#333] rounded-md text-white focus:ring-2 focus:ring-[#00BFFF] focus:border-transparent"
                                              placeholder="Please describe your issue or question in detail..." required></textarea>
                                </div>

                                <div class="flex items-center justify-between">
                                    <p class="text-sm text-gray-400">
                                        <i class="fas fa-clock mr-1"></i>
                                        We typically respond within 24 hours
                                    </p>
                                    <button type="submit" class="px-6 py-2 bg-[#00BFFF] hover:bg-[#0099CC] text-black font-medium rounded-md transition-colors">
                                        <i class="fas fa-paper-plane mr-2"></i>Send Message
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Dashboard View: Overall System Overview (Default) -->
                    <div id="dashboard-overview-view" class="hidden">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold text-white mb-2">System Statistics</h2>
                            <p class="text-gray-400">Key metrics and performance overview</p>
                        </div>

                        <div id="overview-statistics-section" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                            <!-- Stat Card 1: Total Companies -->
                            <div class="bg-[#0A0A0A] p-6 rounded-xl border border-[#1A1A1A] hover:border-[#00BFFF]/30 transition-all duration-300">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-medium text-gray-300">Companies</h3>
                                    <div class="w-8 h-8 rounded-full bg-[#00BFFF]/10 flex items-center justify-center">
                                        <i class="fas fa-building text-[#00BFFF] text-sm"></i>
                                    </div>
                                </div>
                                <p class="text-3xl font-bold text-white mb-2" id="companies-stat">-</p>
                                <p class="text-sm text-gray-400">Total registered</p>
                            </div>

                            <!-- Stat Card 2: Active Agents -->
                            <div class="bg-[#0A0A0A] p-6 rounded-xl border border-[#1A1A1A] hover:border-[#00BFFF]/30 transition-all duration-300">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-medium text-gray-300">Active Agents</h3>
                                    <div class="w-8 h-8 rounded-full bg-green-500/10 flex items-center justify-center">
                                        <i class="fas fa-robot text-green-500 text-sm"></i>
                                    </div>
                                </div>
                                <p class="text-3xl font-bold text-white mb-2" id="active-agents-stat">-</p>
                                <p class="text-sm text-gray-400">Currently running</p>
                            </div>

                            <!-- Stat Card 3: Total Users -->
                            <div class="bg-[#0A0A0A] p-6 rounded-xl border border-[#1A1A1A] hover:border-[#00BFFF]/30 transition-all duration-300">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-lg font-medium text-gray-300">Total Users</h3>
                                    <div class="w-8 h-8 rounded-full bg-purple-500/10 flex items-center justify-center">
                                        <i class="fas fa-users text-purple-500 text-sm"></i>
                                    </div>
                                </div>
                                <p class="text-3xl font-bold text-white mb-2" id="total-users-stat">-</p>
                                <p class="text-sm text-gray-400">Across all companies</p>
                            </div>
                        </div>

                        <!-- Company Selection and Agents List Section -->
                        <div class="bg-[#0A0A0A] p-6 rounded-xl border border-[#1A1A1A] mb-8">
                            <div class="flex items-center justify-between mb-6">
                                <h3 class="text-xl font-semibold text-white">Company Agents</h3>
                                <button id="refresh-overview-agents-btn" class="px-3 py-2 bg-[#333] hover:bg-[#444] text-white rounded-md transition-colors" title="Refresh agents list">
                                    <i class="fas fa-sync-alt"></i>
                                </button>
                            </div>

                            <!-- Company Selection (Hidden for Client role) -->
                            <div id="overview-company-selection" class="mb-6">
                                <label class="block text-sm font-medium text-gray-300 mb-3">Select Company</label>

                                <!-- Hidden input to store selected company -->
                                <input type="hidden" id="overview-company" name="overview-company">

                                <!-- Company Cards Container -->
                                <div id="overview-company-cards-container" class="space-y-3 mb-4">
                                    <!-- Loading state -->
                                    <div id="overview-companies-loading" class="flex items-center justify-center p-6 bg-[#1A1A1A] border border-[#333] rounded-lg">
                                        <div class="flex items-center space-x-3">
                                            <i class="fas fa-spinner fa-spin text-[#00BFFF]"></i>
                                            <span class="text-gray-300">Loading companies...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Separator between company cards and agent cards -->
                            <div class="company-agent-separator my-6">
                                <div class="flex items-center">
                                    <div class="flex-1 h-px bg-gradient-to-r from-transparent via-[#333] to-transparent"></div>
                                    <div class="px-4">
                                        <i class="fas fa-chevron-down text-[#00BFFF] text-sm"></i>
                                    </div>
                                    <div class="flex-1 h-px bg-gradient-to-r from-transparent via-[#333] to-transparent"></div>
                                </div>
                            </div>

                            <!-- Agents List Container -->
                            <div id="overview-agents-container">
                                <!-- Initial state - no company selected -->
                                <div id="overview-no-company-selected" class="flex items-center justify-center p-8 bg-[#1A1A1A] border border-[#333] rounded-lg">
                                    <div class="text-center">
                                        <i class="fas fa-building text-4xl text-gray-500 mb-4"></i>
                                        <h4 class="text-lg font-medium text-gray-300 mb-2">Select a Company</h4>
                                        <p class="text-sm text-gray-400">Choose a company above to view its AI agents</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity Section -->
                        <div class="bg-[#0A0A0A] p-6 rounded-xl border border-[#1A1A1A] mb-8">
                            <h3 class="text-xl font-semibold text-white mb-4">Recent Activity</h3>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between p-3 bg-[#1A1A1A] rounded-lg hover:bg-[#222222] transition-colors">
                                    <div class="flex items-center">
                                        <div class="h-10 w-10 rounded-full bg-[#00BFFF]/10 flex items-center justify-center mr-3">
                                            <span class="text-[#00BFFF]">✓</span>
                                        </div>
                                        <div>
                                            <p class="text-white font-medium">Task completed</p>
                                            <p class="text-sm text-gray-400">Data processing job #1245</p>
                                        </div>
                                    </div>
                                    <span class="text-sm text-gray-400 activity-time">2 mins ago</span>
                                </div>
                                <!-- Add more activity items as needed -->
                            </div>
                        </div>
                    </div>

                    <!-- Dashboard View: Logs -->
                    <div id="dashboard-logs-view" class="hidden">
                        <div class="flex flex-col gap-8">
                            <h2 class="text-4xl font-bold text-white">Agent Activity Logs</h2>
                            <p class="text-lg text-gray-300 max-w-3xl">
                                View detailed logs of agent activities and system events.
                            </p>
                        </div>

                        <div class="mt-8 bg-[#0A0A0A] rounded-xl p-6 shadow-lg border border-[#1A1A1A]">
                            <div class="text-center py-12">
                                <i class="fas fa-scroll text-6xl text-gray-600 mb-4"></i>
                                <h3 class="text-2xl font-bold text-white mb-2">Logs Coming Soon</h3>
                                <p class="text-gray-400">This section is under development and will be available in a future update.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Dashboard View: Support -->
                    <div id="dashboard-support-view" class="hidden">
                        <div class="flex flex-col gap-8">
                            <h2 class="text-4xl font-bold text-white">Contact Support</h2>
                            <p class="text-lg text-gray-300 max-w-3xl">
                                Need help? Send us a message and our team will get back to you as soon as possible.
                            </p>
                        </div>

                        <div class="mt-8 bg-[#0A0A0A] rounded-xl p-6 shadow-lg border border-[#1A1A1A]">
                            <form id="supportForm" class="space-y-6">
                                <div>
                                    <label for="support-email" class="block text-sm font-medium text-gray-300 mb-2">Your Email</label>
                                    <input type="email" id="support-email" name="email" class="w-full px-4 py-3 rounded-lg bg-[#0A0A0A] border-2 border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF]" readonly>
                                </div>
                                
                                <div>
                                    <label for="dashboard-support-subject" class="block text-sm font-medium text-gray-300 mb-2">Subject</label>
                                    <input type="text" id="dashboard-support-subject" name="subject" class="w-full px-4 py-3 rounded-lg bg-[#0A0A0A] border-2 border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF]" placeholder="How can we help you?" required>
                                </div>
                                
                                <div>
                                    <label for="dashboard-support-message" class="block text-sm font-medium text-gray-300 mb-2">Message</label>
                                    <textarea id="dashboard-support-message" name="message" rows="6" class="w-full px-4 py-3 rounded-lg bg-[#0A0A0A] border-2 border-gray-700 text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF]" placeholder="Please describe your issue or question in detail..." required></textarea>
                                </div>
                                
                                <div class="flex items-center justify-between">
                                    <p class="text-sm text-gray-400">We'll respond to your message within 24 hours.</p>
                                    <button type="submit" class="px-6 py-3 bg-[#00BFFF] text-black font-medium rounded-lg hover:bg-[#00FFFF] transition-colors">
                                        <i class="fas fa-paper-plane mr-2"></i>Send Message
                                    </button>
                                </div>
                                
                                <div id="dashboard-support-status" class="mt-4 text-sm"></div>
                            </form>
                        </div>
                    </div>

                    <!-- Company Management View -->
                    <div id="dashboard-company-management-view" class="hidden">
                        <!-- Header Section -->
                        <div class="mb-6 md:mb-8">
                            <h2 class="text-2xl md:text-3xl font-bold text-white mb-2">Company Management</h2>
                            <p class="text-gray-400 text-sm md:text-base">View and manage companies in your organization</p>
                        </div>

                        <!-- Search and Controls Section -->
                        <div class="bg-[#0A0A0A] p-4 md:p-6 rounded-lg border border-[#1A1A1A] mb-4 md:mb-6">
                            <div class="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
                                <!-- Search Bar -->
                                <div class="flex-1 max-w-md md:max-w-lg">
                                    <div class="relative">
                                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10">
                                            <i class="fas fa-search text-gray-400 text-sm"></i>
                                        </div>
                                        <input type="text"
                                               id="company-search-input"
                                               placeholder="Search companies..."
                                               class="w-full pl-10 pr-10 py-3 md:py-2.5 bg-[#1A1A1A] border border-[#333] rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-transparent transition-all duration-200 text-base relative">
                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center z-10">
                                            <button id="clear-company-search-btn" class="hidden text-gray-400 hover:text-white transition-colors p-1 rounded">
                                                <i class="fas fa-times text-sm"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="flex items-center gap-3">
                                    <button id="refresh-companies-btn" class="flex-1 md:flex-none px-4 py-3 md:py-2.5 bg-[#1A1A1A] hover:bg-[#333] text-gray-300 hover:text-white rounded-lg transition-all duration-200 flex items-center justify-center gap-2 min-h-[44px] font-medium">
                                        <i class="fas fa-sync-alt text-sm"></i>
                                        <span class="text-sm md:text-base">Refresh</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Companies Grid Container -->
                        <div class="bg-[#0A0A0A] rounded-lg border border-[#1A1A1A] p-3 md:p-6">
                            <div id="companies-management-container" class="companies-grid">
                                <!-- Loading state -->
                                <div id="companies-management-loading" class="flex items-center justify-center p-6 md:p-8 bg-[#1A1A1A] border border-[#333] rounded-lg">
                                    <div class="flex items-center space-x-3">
                                        <i class="fas fa-spinner fa-spin text-[#00BFFF]"></i>
                                        <span class="text-gray-300 text-sm md:text-base">Loading companies...</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Company Details Modal -->
                        <div id="company-details-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
                            <div class="bg-[#0A0A0A] rounded-lg border border-[#1A1A1A] max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                                <div class="p-6 border-b border-[#1A1A1A] flex items-center justify-between">
                                    <h3 class="text-xl font-semibold text-white">Company Details</h3>
                                    <button id="close-company-details-modal" class="text-gray-400 hover:text-white transition-colors p-2">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                <div id="company-details-modal-content" class="p-6">
                                    <!-- Company details content will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Client Management View -->
                    <div id="dashboard-client-management-view" class="hidden">
                        <div class="mb-8">
                            <h2 class="text-3xl font-bold text-white mb-2">Client Management</h2>
                            <p class="text-gray-400">View and manage client users in your organization</p>
                        </div>

                        <!-- Split Layout Container -->
                        <div class="bg-[#0A0A0A] rounded-lg border border-[#1A1A1A] overflow-hidden">
                            <div class="flex h-[600px]">
                                <!-- Left Side - Client List -->
                                <div class="w-1/2 border-r border-[#1A1A1A] flex flex-col">
                                    <div class="p-6 border-b border-[#1A1A1A]">
                                        <div class="flex items-center justify-between mb-4">
                                            <div>
                                                <h3 class="text-xl font-semibold text-white mb-1">Client Users</h3>
                                                <p class="text-gray-400 text-sm">Select a client to view details</p>
                                            </div>
                                            <button id="refresh-clients-btn" class="px-3 py-2 bg-[#1A1A1A] hover:bg-[#333] text-gray-300 hover:text-white rounded-md transition-colors flex items-center space-x-2">
                                                <i class="fas fa-sync-alt"></i>
                                                <span class="hidden sm:inline">Refresh</span>
                                            </button>
                                        </div>

                                        <!-- Search Bar -->
                                        <div class="relative">
                                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                <i class="fas fa-search text-gray-400"></i>
                                            </div>
                                            <input type="text"
                                                   id="client-search-input"
                                                   placeholder="Search clients by name or company..."
                                                   class="w-full pl-10 pr-4 py-2 bg-[#1A1A1A] border border-[#333] rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-transparent transition-colors">
                                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                                <button id="clear-search-btn" class="hidden text-gray-400 hover:text-white transition-colors">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Clients List Container -->
                                    <div class="flex-1 overflow-y-auto">
                                        <div id="clients-management-container" class="p-4">
                                            <!-- Loading state -->
                                            <div id="clients-management-loading" class="flex items-center justify-center p-8 bg-[#1A1A1A] border border-[#333] rounded-lg">
                                                <div class="flex items-center space-x-3">
                                                    <i class="fas fa-spinner fa-spin text-[#00BFFF]"></i>
                                                    <span class="text-gray-300">Loading clients...</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Right Side - Client Details -->
                                <div class="w-1/2 flex flex-col">
                                    <div class="p-6 border-b border-[#1A1A1A]">
                                        <h3 class="text-xl font-semibold text-white">Client Details</h3>
                                        <p class="text-gray-400 text-sm">Detailed information about the selected client</p>
                                    </div>

                                    <div class="flex-1 overflow-y-auto">
                                        <div id="client-details-panel" class="p-6">
                                            <!-- Default state when no client is selected -->
                                            <div id="client-details-empty" class="flex items-center justify-center h-full">
                                                <div class="text-center">
                                                    <i class="fas fa-user-circle text-4xl text-gray-500 mb-4"></i>
                                                    <h4 class="text-lg font-medium text-gray-300 mb-2">No Client Selected</h4>
                                                    <p class="text-sm text-gray-400">Select a client from the list to view their details</p>
                                                </div>
                                            </div>

                                            <!-- Client details content will be populated here -->
                                            <div id="client-details-content" class="hidden"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> <!-- End dashboard-views-container -->
            </div> <!-- End Dashboard Content Area -->
        </div> <!-- End Dashboard Flex Container -->
    </section>

    
    <style>
        @keyframes spin-slow {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .animate-spin-slow {
            animation: spin-slow 2s linear infinite;
        }
        #loading-overlay {
            transition: opacity 0.5s ease-in-out, visibility 0.5s ease-in-out;
        }



        #company option {
            background-color: #1A1A1A;
            color: white;
            padding: 0.5rem;
        }

        #company option[value="__new_company__"] {
            color: #00BFFF;
            font-style: italic;
            border-top: 1px solid #333;
        }

        #company option[value="__retry__"] {
            color: #fbbf24;
            font-style: italic;
        }

        /* Company Cards Container Layouts - Grid for compact display */
        #company-cards-container.space-y-3,
        #overview-company-cards-container.space-y-3 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1rem;
            margin: 0; /* Reset margin from space-y-3 */
        }

        /* Company Cards Styling */
        .company-card {
            transition: all 0.2s ease-in-out;
            max-width: 500px; /* Limit maximum width for better layout */
            margin: 0; /* Reset any margin from space-y-3 */
        }

        .company-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 191, 255, 0.15);
        }

        .company-card.selected {
            border-color: #00BFFF !important;
            background-color: rgba(0, 191, 255, 0.1) !important;
            box-shadow: 0 0 0 1px rgba(0, 191, 255, 0.3);
        }

        /* Overview Company Cards Styling */
        #overview-company-cards-container .company-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out, border-color 0.2s ease-in-out;
        }

        #overview-company-cards-container .company-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 191, 255, 0.15);
        }

        /* Company-Agent Separator Styling */
        .company-agent-separator {
            opacity: 0.8;
            transition: opacity 0.3s ease-in-out;
        }

        .company-agent-separator:hover {
            opacity: 1;
        }

        .company-agent-separator i {
            transition: transform 0.3s ease-in-out, color 0.3s ease-in-out;
        }

        .company-agent-separator:hover i {
            transform: translateY(2px);
            color: #00FFFF;
        }

        /* Custom Scrollbar Styling - Always Visible */
        /* Webkit browsers (Chrome, Safari, Edge) */
        ::-webkit-scrollbar {
            width: 14px;
            height: 14px;
            -webkit-appearance: none;
        }

        ::-webkit-scrollbar-track {
            background: rgba(26, 26, 26, 0.4);
            border-radius: 7px;
            border: 1px solid rgba(51, 51, 51, 0.3);
        }

        ::-webkit-scrollbar-thumb {
            background: #444;
            border-radius: 7px;
            border: 2px solid rgba(26, 26, 26, 0.4);
            background-clip: content-box;
            transition: background-color 0.3s ease;
            min-height: 30px; /* Ensure minimum thumb size */
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #555;
            background-clip: content-box;
        }

        ::-webkit-scrollbar-thumb:active {
            background: #00BFFF;
            background-clip: content-box;
        }

        ::-webkit-scrollbar-corner {
            background: rgba(26, 26, 26, 0.4);
        }

        /* Scrollbar visibility settings */
        html {
            scrollbar-gutter: stable; /* Reserve space for scrollbar when it appears */
        }

        body {
            overflow-x: auto; /* Allow horizontal scrolling when needed */
        }

        /* Firefox */
        * {
            scrollbar-width: auto; /* Use auto instead of thin for better visibility */
            scrollbar-color: #444 rgba(26, 26, 26, 0.4);
        }

        /* For better scrollbar styling in Firefox */
        html {
            scrollbar-color: #444 rgba(26, 26, 26, 0.4);
            scrollbar-width: auto;
        }

        /* Dashboard specific scrollbar styling */
        #ai-dashboard-page .overflow-y-auto {
            scrollbar-gutter: stable; /* Reserve space for scrollbar when needed */
        }

        #ai-dashboard-page .overflow-y-auto::-webkit-scrollbar {
            width: 14px;
        }

        #ai-dashboard-page .overflow-y-auto::-webkit-scrollbar-thumb {
            background: #555;
            border: 1px solid #1A1A1A;
            border-radius: 7px;
        }

        #ai-dashboard-page .overflow-y-auto::-webkit-scrollbar-thumb:hover {
            background: #666;
            border: 1px solid #00BFFF;
        }

        #ai-dashboard-page .overflow-y-auto::-webkit-scrollbar-track {
            background: rgba(26, 26, 26, 0.5);
            border-radius: 7px;
            border: 1px solid rgba(51, 51, 51, 0.3);
        }

        /* Modal scrollbar styling */
        .modal-content::-webkit-scrollbar-thumb {
            background: #444;
            border: 1px solid #1A1A1A;
        }

        .modal-content::-webkit-scrollbar-thumb:hover {
            background: #00BFFF;
        }

        /* Overview Agents Styling */
        #overview-agents-container {
            transition: none;
            transform: none !important;
        }

        /* Prevent any unwanted animations on the overview section */
        #dashboard-overview-view {
            transform: none !important;
        }

        /* Agent cards container - use grid layout for more compact display */
        #overview-agents-container .space-y-3 {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 0.5fr));
            gap: 1rem;
            margin: 0; /* Reset margin from space-y-3 */
        }

        /* Individual agent card styling */
        #overview-agents-container .agent-card {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
            max-width: 400px; /* Reduced maximum width for half size */
            margin: 0; /* Reset any margin from space-y-3 */
        }

        #overview-agents-container .agent-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 191, 255, 0.1);
        }

        /* AI Agent Management section styling */
        #ai-agents-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 1rem;
        }

        #ai-agents-container .agent-card {
            max-width: 600px; /* Limit maximum width */
            margin: 0 !important; /* Override any margin from mb-3 class */
        }

        /* Mobile-first responsive design */
        @media (max-width: 480px) {
            .companies-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            .company-management-card {
                padding: 1rem !important;
            }

            .company-actions {
                flex-direction: column;
                gap: 0.5rem;
            }

            .company-actions button {
                width: 100%;
                justify-content: center;
            }
        }

        @media (max-width: 768px) {
            #overview-agents-container .space-y-3,
            #ai-agents-container,
            .companies-grid,
            #company-cards-container.space-y-3,
            #overview-company-cards-container.space-y-3 {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }

            #overview-agents-container .agent-card,
            #ai-agents-container .agent-card,
            .company-management-card,
            .company-card {
                max-width: none;
            }

            .companies-grid {
                grid-template-columns: 1fr;
            }
        }

        @media (min-width: 769px) and (max-width: 1024px) {
            .companies-grid {
                grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            }
        }

        @media (min-width: 1025px) {
            .companies-grid {
                grid-template-columns: repeat(auto-fit, minmax(450px, 600px));
            }

            #overview-agents-container .space-y-3 {
                grid-template-columns: repeat(auto-fit, minmax(300px, 0.45fr));
            }

            #ai-agents-container {
                grid-template-columns: repeat(auto-fit, minmax(450px, 600px));
            }

            #company-cards-container.space-y-3,
            #overview-company-cards-container.space-y-3 {
                grid-template-columns: repeat(auto-fit, minmax(350px, 500px));
            }
        }

        /* Prevent bouncing during content changes */
        #overview-agents-container > * {
            animation: none !important;
        }

        /* Smooth content loading */
        .overview-content-loading {
            opacity: 0.7;
            transition: opacity 0.3s ease-in-out;
        }

        .overview-content-loaded {
            opacity: 1;
            transition: opacity 0.3s ease-in-out;
        }



        /* Loading animation */
        @keyframes pulse-loading {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Statistics update animation */
        @keyframes statUpdate {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); color: #00BFFF; }
            100% { transform: scale(1); }
        }
        .stat-updated {
            animation: statUpdate 0.5s ease-in-out;
        }

        #companies-loading,
        #overview-companies-loading {
            animation: pulse-loading 2s ease-in-out infinite;
        }

        /* Delete Modal Styling */
        #deleteAgentModal .modal-content {
            border-color: #dc2626 !important;
            box-shadow: 0 20px 25px -5px rgba(220, 38, 38, 0.1), 0 10px 10px -5px rgba(220, 38, 38, 0.04);
        }

        #deleteAgentModal .modal-content:hover {
            box-shadow: 0 25px 50px -12px rgba(220, 38, 38, 0.25);
        }

        #confirm-delete-agent:hover {
            background-color: #b91c1c !important;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(220, 38, 38, 0.4);
        }

        #confirm-delete-agent:active {
            transform: translateY(0);
        }

        /* Company Management Grid System */
        .companies-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 1rem;
        }

        /* Company Management Card Styling */
        .company-management-card {
            transition: all 0.2s ease-in-out;
            max-width: 100%;
            margin: 0 !important;
            position: relative;
        }

        .company-management-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 191, 255, 0.15);
        }

        .company-management-card.selected {
            border-color: #00BFFF !important;
            background-color: rgba(0, 191, 255, 0.1) !important;
            box-shadow: 0 0 0 1px rgba(0, 191, 255, 0.3);
        }

        .company-management-card.expanded {
            border-color: #00BFFF;
            box-shadow: 0 8px 25px rgba(0, 191, 255, 0.2);
        }

        /* Company Action Buttons */
        .company-actions button {
            transition: all 0.2s ease-in-out;
        }

        .company-actions button:hover {
            transform: translateY(-1px);
        }

        .company-actions button:active {
            transform: translateY(0);
        }

        /* Hide expand indicators and inline details in list view */
        .company-management-card .expand-indicator {
            display: none;
        }

        .company-details {
            display: none;
        }

        /* Company Details Panel Styling */
        #company-details-panel {
            background: #0A0A0A;
        }

        #company-details-content {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Company Details Animation States */
        .company-details-entering {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
        }

        .company-details-entered {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .company-details-exiting {
            opacity: 0;
            transform: translateY(-10px) scale(1.02);
        }

        /* Loading animation for company details */
        .company-details-loading {
            animation: pulse 1.5s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        /* Smooth transitions for company cards */
        .company-management-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .company-management-card.selecting {
            transform: scale(1.02);
            border-color: #00BFFF;
            background: rgba(0, 191, 255, 0.1);
        }

        /* Company Search Styling */
        #company-search-input {
            transition: all 0.3s ease-in-out;
        }

        #company-search-input:focus {
            box-shadow: 0 0 0 3px rgba(0, 191, 255, 0.1);
        }

        #clear-company-search-btn {
            transition: all 0.2s ease-in-out;
        }

        #clear-company-search-btn:hover {
            transform: scale(1.1);
        }

        /* Responsive adjustments for company management */
        @media (max-width: 1024px) {
            #dashboard-company-management-view .flex {
                flex-direction: column;
                height: auto;
            }

            #dashboard-company-management-view .w-1\/2 {
                width: 100%;
            }

            #dashboard-company-management-view .border-r {
                border-right: none;
                border-bottom: 1px solid #1A1A1A;
            }

            #dashboard-company-management-view .flex-1 {
                max-height: 300px;
            }

            #company-search-input {
                font-size: 16px; /* Prevent zoom on iOS */
            }
        }

        /* Client Management Styling */
        #clients-management-container {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .client-management-card {
            transition: all 0.2s ease-in-out;
            border: 1px solid #333;
            background: #1A1A1A;
            cursor: pointer;
            border-radius: 8px;
            padding: 1rem 1.25rem;
            max-width: 100%;
            margin: 0 !important;
            position: relative;
        }

        .client-management-card:hover {
            border-color: #00BFFF;
            background: rgba(0, 191, 255, 0.05);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 191, 255, 0.15);
        }

        .client-management-card.selected {
            border-color: #00BFFF !important;
            background: rgba(0, 191, 255, 0.1) !important;
            box-shadow: 0 0 0 1px rgba(0, 191, 255, 0.3);
        }

        .client-management-card.expanded {
            border-color: #00BFFF;
            box-shadow: 0 8px 25px rgba(0, 191, 255, 0.2);
        }

        .client-management-card .expand-indicator {
            display: none; /* Hide expand indicators in list view */
        }

        .client-details {
            display: none; /* Hide inline details in list view */
        }

        /* Client Details Panel Styling */
        #client-details-panel {
            background: #0A0A0A;
        }

        #client-details-content {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Client Details Animation States */
        .client-details-entering {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
        }

        .client-details-entered {
            opacity: 1;
            transform: translateY(0) scale(1);
        }

        .client-details-exiting {
            opacity: 0;
            transform: translateY(-10px) scale(1.02);
        }

        /* Loading animation for client details */
        .client-details-loading {
            animation: pulse 1.5s ease-in-out infinite;
        }

        /* Smooth transitions for client cards */
        .client-management-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .client-management-card.selecting {
            transform: scale(1.02);
            border-color: #00BFFF;
            background: rgba(0, 191, 255, 0.1);
        }

        /* Mobile responsive adjustments for client cards */
        @media (max-width: 480px) {
            .client-management-card {
                padding: 1rem !important;
            }
        }

        /* Client Search Styling */
        #client-search-input {
            transition: all 0.3s ease-in-out;
        }

        #client-search-input:focus {
            box-shadow: 0 0 0 3px rgba(0, 191, 255, 0.1);
        }

        #clear-search-btn {
            transition: all 0.2s ease-in-out;
        }

        #clear-search-btn:hover {
            transform: scale(1.1);
        }

        .no-search-results {
            animation: fadeIn 0.3s ease-in-out;
        }

        /* Responsive adjustments for client management */
        @media (max-width: 1024px) {
            #dashboard-client-management-view .flex {
                flex-direction: column;
                height: auto;
            }

            #dashboard-client-management-view .w-1\/2 {
                width: 100%;
            }

            #dashboard-client-management-view .border-r {
                border-right: none;
                border-bottom: 1px solid #1A1A1A;
            }

            #dashboard-client-management-view .flex-1 {
                max-height: 300px;
            }

            #client-search-input {
                font-size: 16px; /* Prevent zoom on iOS */
            }
        }

        /* Notifications Page Styling */
        .notification-filter-tab {
            color: #9CA3AF;
            background: transparent;
        }

        .notification-filter-tab.active {
            color: #00BFFF;
            background: rgba(0, 191, 255, 0.1);
        }

        .notification-filter-tab:hover:not(.active) {
            color: #FFFFFF;
            background: rgba(255, 255, 255, 0.05);
        }

        .notification-item {
            transition: all 0.3s ease-in-out;
            border: 1px solid #333;
            background: #1A1A1A;
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
        }

        .notification-item:hover {
            border-color: #00BFFF;
            background: rgba(0, 191, 255, 0.05);
        }

        .notification-item.unread {
            border-left: 4px solid #00BFFF;
            background: rgba(0, 191, 255, 0.02);
        }

        .notification-item.read {
            opacity: 0.7;
        }

        .notification-item .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .notification-item .notification-icon.system {
            background: rgba(59, 130, 246, 0.1);
            color: #3B82F6;
        }

        .notification-item .notification-icon.integration {
            background: rgba(34, 197, 94, 0.1);
            color: #22C55E;
        }

        .notification-item .notification-icon.agent {
            background: rgba(168, 85, 247, 0.1);
            color: #A855F7;
        }

        .notification-item .notification-icon.user {
            background: rgba(0, 191, 255, 0.1);
            color: #00BFFF;
        }

        .notification-item .notification-icon.error {
            background: rgba(239, 68, 68, 0.1);
            color: #EF4444;
        }

        .notification-item .notification-icon.success {
            background: rgba(34, 197, 94, 0.1);
            color: #22C55E;
        }

        .notification-item .notification-icon.warning {
            background: rgba(245, 158, 11, 0.1);
            color: #F59E0B;
        }

        .notification-item .notification-time {
            font-size: 0.75rem;
            color: #6B7280;
        }

        .notification-item.unread .notification-time {
            color: #00BFFF;
        }

        #notification-badge {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.7;
            }
        }

        /* Responsive adjustments for notifications */
        @media (max-width: 768px) {
            .notification-filter-tab {
                padding: 0.5rem 0.75rem;
                font-size: 0.75rem;
            }

            .notification-item {
                padding: 0.75rem;
            }

            .notification-item .notification-icon {
                width: 32px;
                height: 32px;
            }
        }

        /* Integrations Page Styling */
        .integration-card {
            transition: all 0.3s ease-in-out;
            position: relative;
            overflow: hidden;
        }

        .integration-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 191, 255, 0.15);
        }

        .integration-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 191, 255, 0.1), transparent);
            transition: left 0.5s ease-in-out;
        }

        .integration-card:hover::before {
            left: 100%;
        }

        .status-badge {
            font-size: 0.75rem;
            font-weight: 500;
            transition: all 0.3s ease-in-out;
        }

        .status-badge.connected {
            background-color: #10B981 !important;
            color: white !important;
        }

        .status-badge.connecting {
            background-color: #F59E0B !important;
            color: white !important;
            animation: pulse 2s infinite;
        }

        .status-badge.disconnecting {
            background-color: #EF4444 !important;
            color: white !important;
            animation: pulse 2s infinite;
        }

        .connect-btn {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease-in-out;
        }

        .connect-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 191, 255, 0.3);
        }

        .connect-btn.connected {
            background-color: #10B981 !important;
            color: white !important;
        }

        .connect-btn.connected:hover {
            background-color: #059669 !important;
        }

        .connect-btn.connecting {
            background-color: #F59E0B !important;
            color: white !important;
            cursor: not-allowed;
        }

        .connect-btn.connecting:hover {
            background-color: #D97706 !important;
            transform: none;
            box-shadow: none;
        }

        /* Integration category headers */
        .integration-category h3 i {
            transition: transform 0.3s ease-in-out;
        }

        .integration-category:hover h3 i {
            transform: scale(1.1);
        }

        /* Responsive adjustments for integrations */
        @media (max-width: 768px) {
            .integration-card {
                margin-bottom: 1rem;
            }

            .integration-card .grid {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 1024px) {
            .integration-card .grid.lg\\:grid-cols-3 {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        /* Dashboard Loading Overlay Styles */
        #dashboard-loading-overlay {
            backdrop-filter: blur(10px);
            transition: opacity 0.5s ease-in-out, visibility 0.5s ease-in-out;
        }

        #dashboard-loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        #dashboard-loading-overlay.hide {
            opacity: 0;
            visibility: hidden;
        }

        /* Enhanced loading animations */
        @keyframes dashboardPulse {
            0%, 100% {
                opacity: 0.6;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.05);
            }
        }

        @keyframes progressGlow {
            0%, 100% {
                box-shadow: 0 0 5px rgba(0, 191, 255, 0.5);
            }
            50% {
                box-shadow: 0 0 20px rgba(0, 191, 255, 0.8), 0 0 30px rgba(0, 255, 255, 0.4);
            }
        }

        #dashboard-loading-progress {
            animation: progressGlow 2s ease-in-out infinite;
        }

        /* Loading step animations */
        .loading-step-active {
            color: #00BFFF !important;
            font-weight: 500;
        }

        .loading-step-completed {
            color: #10B981 !important;
        }

        .loading-step-icon-show {
            opacity: 1 !important;
            transition: opacity 0.3s ease-in-out;
        }

        /* Dashboard initializing state styles */
        .dashboard-initializing {
            position: relative;
        }

        .dashboard-initializing::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.3);
            z-index: 10;
            pointer-events: none;
        }

        .dashboard-initializing::after {
            content: 'Initializing...';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #00BFFF;
            font-size: 1.2rem;
            font-weight: bold;
            z-index: 11;
            pointer-events: none;
        }
    </style>

    <!-- Dashboard Loading Overlay -->
    <div id="dashboard-loading-overlay" class="fixed inset-0 bg-black bg-opacity-95 z-50 flex items-center justify-center hidden">
        <div class="text-center">
            <!-- Main Loading Animation -->
            <div class="mb-8">
                <div class="relative">
                    <!-- Outer rotating ring -->
                    <div class="w-24 h-24 border-4 border-gray-700 border-t-[#00BFFF] rounded-full animate-spin mx-auto"></div>
                    <!-- Inner pulsing circle -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="w-12 h-12 bg-[#00BFFF] rounded-full animate-pulse opacity-60"></div>
                    </div>
                    <!-- Center icon -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <i class="fas fa-robot text-white text-2xl"></i>
                    </div>
                </div>
            </div>

            <!-- Loading Text -->
            <div class="mb-6">
                <h3 id="dashboard-loading-title" class="text-2xl font-bold text-white mb-2">Initializing Dashboard</h3>
                <p id="dashboard-loading-status" class="text-gray-300 text-lg">Setting up your workspace...</p>
            </div>

            <!-- Progress Bar -->
            <div class="w-80 mx-auto mb-4">
                <div class="bg-gray-700 rounded-full h-2 overflow-hidden">
                    <div id="dashboard-loading-progress" class="bg-gradient-to-r from-[#00BFFF] to-[#00FFFF] h-full rounded-full transition-all duration-500 ease-out" style="width: 0%"></div>
                </div>
            </div>

            <!-- Loading Steps -->
            <div class="text-sm text-gray-400 space-y-1">
                <div id="loading-step-auth" class="flex items-center justify-center space-x-2">
                    <i class="fas fa-check text-green-500 opacity-0"></i>
                    <span>Authenticating user</span>
                </div>
                <div id="loading-step-profile" class="flex items-center justify-center space-x-2">
                    <i class="fas fa-spinner fa-spin text-[#00BFFF] opacity-0"></i>
                    <span>Loading profile</span>
                </div>
                <div id="loading-step-permissions" class="flex items-center justify-center space-x-2">
                    <i class="fas fa-spinner fa-spin text-[#00BFFF] opacity-0"></i>
                    <span>Setting up permissions</span>
                </div>
                <div id="loading-step-dashboard" class="flex items-center justify-center space-x-2">
                    <i class="fas fa-spinner fa-spin text-[#00BFFF] opacity-0"></i>
                    <span>Preparing dashboard</span>
                </div>
            </div>
        </div>
    </div>



    <!-- Delete Agent Confirmation Modal -->
    <div id="deleteAgentModal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="modal-content bg-[#0A0A0A] rounded-lg border border-red-600 p-6 w-full max-w-md mx-4">
            <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-exclamation-triangle text-red-400 text-xl"></i>
                    <h3 class="text-xl font-semibold text-white">Delete AI Agent</h3>
                </div>
                <button class="modal-close text-gray-400 hover:text-white text-2xl font-bold">&times;</button>
            </div>

            <div class="mb-6">
                <p class="text-gray-300 mb-4">Are you sure you want to delete this AI agent?</p>
                <div class="bg-[#1A1A1A] p-4 rounded-lg border border-[#333]">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 rounded-full bg-red-500/10 flex items-center justify-center">
                            <i class="fas fa-robot text-red-400"></i>
                        </div>
                        <div>
                            <h4 id="delete-agent-name" class="text-white font-medium">Agent Name</h4>
                            <p id="delete-agent-company" class="text-sm text-gray-400">Company Name</p>
                        </div>
                    </div>
                </div>
                <p class="text-red-300 text-sm mt-4">
                    <i class="fas fa-warning mr-2"></i>
                    This action cannot be undone.
                </p>
            </div>

            <div class="flex items-center justify-end space-x-3">
                <button class="modal-close px-4 py-2 bg-[#333] hover:bg-[#444] text-white rounded-lg transition-colors">
                    Cancel
                </button>
                <button id="confirm-delete-agent" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors flex items-center space-x-2">
                    <i class="fas fa-trash"></i>
                    <span>Delete Agent</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Profile Edit Modal -->
    <div id="profileEditModal" class="modal fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style="display: none;">
        <div class="modal-content bg-[#0A0A0A] rounded-lg border border-[#1A1A1A] p-6 w-full max-w-md mx-4">
            <div class="flex items-center justify-between mb-6">
                <h3 class="text-xl font-semibold text-white">Profile</h3>
                <button class="modal-close text-gray-400 hover:text-white text-2xl font-bold">&times;</button>
            </div>

            <form id="profileEditForm" class="space-y-4">
                <div>
                    <label for="edit-full-name" class="block text-sm font-medium text-gray-300 mb-2">Full Name</label>
                    <input type="text" id="edit-full-name" name="full_name" class="w-full px-4 py-3 rounded-lg bg-[#1A1A1A] border border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-transparent" required>
                </div>

                <div>
                    <label for="edit-company" class="block text-sm font-medium text-gray-300 mb-2">Company Name</label>
                    <input type="text" id="edit-company" name="company" class="w-full px-4 py-3 rounded-lg bg-[#1A1A1A] border border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-transparent" required>
                </div>

                <div>
                    <label for="edit-email" class="block text-sm font-medium text-gray-300 mb-2">Email Address</label>
                    <input type="email" id="edit-email" name="email" class="w-full px-4 py-3 rounded-lg bg-[#1A1A1A] border border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-transparent" required>
                </div>

                <div class="border-t border-[#333] pt-4">
                    <h4 class="text-sm font-medium text-gray-300 mb-3">Change Password (Optional)</h4>
                    <div class="space-y-3">
                        <div>
                            <label for="edit-new-password" class="block text-xs text-gray-400 mb-1">New Password</label>
                            <input type="password" id="edit-new-password" name="new_password" class="w-full px-3 py-2 rounded-lg bg-[#1A1A1A] border border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-transparent text-sm" placeholder="Leave blank to keep current password">
                        </div>
                        <div>
                            <label for="edit-confirm-password" class="block text-xs text-gray-400 mb-1">Confirm New Password</label>
                            <input type="password" id="edit-confirm-password" name="confirm_password" class="w-full px-3 py-2 rounded-lg bg-[#1A1A1A] border border-[#333] text-white focus:outline-none focus:ring-2 focus:ring-[#00BFFF] focus:border-transparent text-sm" placeholder="Confirm new password">
                        </div>
                    </div>
                </div>

                <div class="flex items-center justify-end space-x-3 pt-4">
                    <button type="button" class="modal-close w-10 h-10 rounded-lg bg-[#1A1A1A] border border-[#333] text-gray-400 hover:text-white hover:bg-[#333] transition-colors flex items-center justify-center">
                        <i class="fas fa-times"></i>
                    </button>
                    <button type="submit" class="px-6 py-2 bg-[#00BFFF] hover:bg-[#0099CC] text-black font-medium rounded-lg transition-colors">
                        <span class="button-text">Save Changes</span>
                        <i class="fas fa-spinner fa-spin hidden loading-icon"></i>
                    </button>
                </div>

                <!-- Error/Success Messages -->
                <div id="profile-edit-message" class="hidden mt-4 p-3 rounded-lg text-sm"></div>
            </form>
        </div>
    </div>



    <!-- JavaScript files are loaded dynamically by loadScript() above -->

    <!-- Supabase Client -->
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

    <!-- Application JavaScript Modules -->
    <script src="js/utils.js"></script>
    <script src="js/api.js"></script>
    <script src="js/auth.js"></script>
    <script src="js/dashboard.js"></script>
    <script src="js/modals.js"></script>
    <script src="js/constellation.js"></script>
    <script src="js/main.js"></script>
    <script src="js/profile-debug.js"></script>

    <!-- All JavaScript functionality has been moved to separate modules -->
</body>
</html>